# 基于 GrapesJS 的图书编辑器开发计划

## 项目概述

基于需求分析文档，本项目将分四个阶段开发一个功能完整的图书编辑器。总开发周期为 17 周，采用敏捷开发模式，每个阶段都有明确的交付物和验收标准。

## 技术栈确认

### 核心技术
- **GrapesJS**: v0.22.12（核心编辑器框架）
- **前端框架**: 原生 JavaScript ES6+ / TypeScript
- **构建工具**: Vite 4.x
- **包管理**: pnpm
- **样式**: SCSS + CSS3

### 依赖库
- **GrapesJS 插件**:
  - `grapesjs-blocks-basic`: 基础块组件
  - `grapesjs-plugin-forms`: 表单组件
  - `grapesjs-preset-webpage`: 网页预设
- **导出库**:
  - `jspdf`: PDF 导出
  - `jszip`: 文件打包
  - `html2canvas`: HTML 转图片
- **工具库**:
  - `lodash`: 工具函数
  - `uuid`: 唯一标识生成

## 开发阶段规划

### 第一阶段：基础框架搭建（4周）

#### 1.1 项目初始化（第1周）
**任务清单**:
- [ ] 创建项目目录结构
- [ ] 初始化 package.json 和依赖管理
- [ ] 配置 Vite 构建环境
- [ ] 设置 TypeScript 配置
- [ ] 配置代码规范（ESLint + Prettier）
- [ ] 设置 Git 仓库和分支策略

**技术要点**:
```bash
# 项目初始化命令
npm create vite@latest book-editor --template vanilla-ts
cd book-editor
pnpm install
pnpm add grapesjs grapesjs-blocks-basic
```

**交付物**:
- 完整的项目脚手架
- 基础构建配置
- 开发环境搭建文档

#### 1.2 GrapesJS 基础集成（第2周）
**任务清单**:
- [ ] GrapesJS 基础初始化
- [ ] 基本编辑器配置
- [ ] 插件加载机制
- [ ] 基础事件监听

**核心代码结构**:
```javascript
// src/editor/index.ts
import grapesjs from 'grapesjs';
import 'grapesjs/dist/css/grapes.min.css';

export class BookEditor {
  private editor: any;
  
  constructor(container: string) {
    this.editor = grapesjs.init({
      container,
      height: '100%',
      storageManager: false,
      plugins: ['gjs-blocks-basic'],
      canvas: {
        styles: ['./src/styles/canvas.css']
      }
    });
  }
}
```

#### 1.3 基础 UI 布局（第3周）
**任务清单**:
- [ ] 三栏布局实现（章节导航 + 编辑器 + 属性面板）
- [ ] 响应式设计
- [ ] 基础样式系统
- [ ] 工具栏设计

**布局结构**:
```html
<div class="book-editor">
  <header class="editor-header">
    <!-- 顶部工具栏 -->
  </header>
  <div class="editor-body">
    <aside class="sidebar-left">
      <!-- 章节导航 -->
    </aside>
    <main class="editor-main">
      <!-- GrapesJS 编辑器 -->
    </main>
    <aside class="sidebar-right">
      <!-- 属性面板 -->
    </aside>
  </div>
  <footer class="editor-footer">
    <!-- 状态栏 -->
  </footer>
</div>
```

#### 1.4 基础配置和测试（第4周）
**任务清单**:
- [ ] 编辑器配置优化
- [ ] 基础功能测试
- [ ] 性能基准测试
- [ ] 文档编写

**里程碑 M1**: GrapesJS 基础集成完成
- 编辑器可正常启动和运行
- 基础 UI 布局完成
- 基本的拖拽编辑功能可用

### 第二阶段：核心编辑功能（6周）

#### 2.1 自定义组件开发（第5-6周）
**任务清单**:
- [ ] 图书章节组件（Chapter Component）
- [ ] 页面组件（Page Component）
- [ ] 段落组件（Paragraph Component）
- [ ] 标题组件（Heading Component）

**章节组件示例**:
```javascript
editor.DomComponents.addType('book-chapter', {
  isComponent: (el) => el.classList?.contains('book-chapter'),
  model: {
    defaults: {
      tagName: 'section',
      attributes: { class: 'book-chapter' },
      name: '章节',
      draggable: '.book-container',
      droppable: '.book-page, .book-paragraph',
      traits: [
        'id',
        { type: 'text', name: 'title', label: '章节标题' },
        { type: 'number', name: 'order', label: '章节序号' }
      ]
    }
  },
  view: {
    onRender() {
      // 章节渲染逻辑
    }
  }
});
```

#### 2.2 自定义块定义（第7-8周）
**任务清单**:
- [ ] 标题块（H1-H6）
- [ ] 正文段落块
- [ ] 引用块
- [ ] 代码块
- [ ] 图片块
- [ ] 表格块

**块定义示例**:
```javascript
editor.BlockManager.add('book-heading', {
  label: '标题',
  category: '图书元素',
  media: '<i class="fa fa-header"></i>',
  content: {
    type: 'book-heading',
    tagName: 'h1',
    content: '章节标题',
    attributes: { class: 'book-heading' }
  },
  activate: true
});
```

#### 2.3 章节管理功能（第9周）
**任务清单**:
- [ ] 章节树形结构
- [ ] 拖拽排序功能
- [ ] 章节增删改操作
- [ ] 章节导航同步

#### 2.4 富文本编辑器集成（第10周）
**任务清单**:
- [ ] 替换默认 RTE
- [ ] 自定义工具栏
- [ ] 格式化功能
- [ ] 特殊字符支持

**里程碑 M2**: 核心编辑功能完成
- 自定义组件和块可正常使用
- 章节管理功能完整
- 富文本编辑体验良好

### 第三阶段：高级功能开发（4周）

#### 3.1 模板系统（第11-12周）
**任务清单**:
- [ ] 页面模板引擎
- [ ] 预设模板库
- [ ] 自定义模板保存
- [ ] 模板应用机制

#### 3.2 导出功能（第13-14周）
**任务清单**:
- [ ] HTML 导出
- [ ] PDF 导出（使用 jsPDF）
- [ ] EPUB 导出基础
- [ ] Word 导出（HTML 转换）

**PDF 导出示例**:
```javascript
class PDFExporter {
  async exportToPDF(editor: any) {
    const html = editor.getHtml();
    const css = editor.getCss();
    
    // 使用 html2canvas + jsPDF
    const canvas = await html2canvas(document.querySelector('.gjs-frame'));
    const pdf = new jsPDF();
    pdf.addImage(canvas.toDataURL('image/png'), 'PNG', 0, 0);
    pdf.save('book.pdf');
  }
}
```

**里程碑 M3**: 导出和模板功能完成
- 模板系统可正常使用
- 支持多种格式导出
- 导出质量满足要求

### 第四阶段：测试和优化（3周）

#### 4.1 测试开发（第15周）
**任务清单**:
- [ ] 单元测试（Jest）
- [ ] 集成测试
- [ ] E2E 测试（Playwright）
- [ ] 性能测试

#### 4.2 优化和完善（第16周）
**任务清单**:
- [ ] 性能优化
- [ ] 内存泄漏检查
- [ ] 用户体验优化
- [ ] 错误处理完善

#### 4.3 文档和部署（第17周）
**任务清单**:
- [ ] 用户使用文档
- [ ] 开发者文档
- [ ] 部署配置
- [ ] 发布准备

**里程碑 M4**: 产品发布就绪
- 所有功能测试通过
- 性能指标达标
- 文档完整
- 可正式发布

## 技术实现要点

### 1. 项目结构
```
book-editor/
├── src/
│   ├── components/          # 自定义组件
│   ├── blocks/             # 自定义块
│   ├── plugins/            # 插件
│   ├── templates/          # 模板
│   ├── exporters/          # 导出器
│   ├── utils/              # 工具函数
│   ├── styles/             # 样式文件
│   └── main.ts             # 入口文件
├── public/                 # 静态资源
├── tests/                  # 测试文件
└── docs/                   # 文档
```

### 2. 核心配置
```javascript
const editorConfig = {
  container: '#book-editor',
  height: '100%',
  storageManager: {
    type: 'local',
    autosave: true,
    stepsBeforeSave: 3
  },
  blockManager: {
    appendTo: '.blocks-container'
  },
  styleManager: {
    appendTo: '.styles-container',
    sectors: [
      {
        name: '排版',
        buildProps: ['font-family', 'font-size', 'line-height', 'text-align']
      },
      {
        name: '布局',
        buildProps: ['margin', 'padding', 'width', 'height']
      }
    ]
  },
  layerManager: {
    appendTo: '.layers-container'
  },
  traitManager: {
    appendTo: '.traits-container'
  }
};
```

## 风险管理

### 技术风险
1. **GrapesJS 学习曲线陡峭**
   - 缓解措施：提前技术调研，建立原型
   
2. **自定义组件开发复杂**
   - 缓解措施：分阶段开发，先简单后复杂
   
3. **导出功能技术难度高**
   - 缓解措施：使用成熟第三方库，分格式逐步实现

### 进度风险
1. **功能开发超时**
   - 缓解措施：每周进度检查，及时调整计划
   
2. **测试时间不足**
   - 缓解措施：开发过程中同步测试

## 质量保证

### 代码质量
- TypeScript 类型检查
- ESLint 代码规范
- 单元测试覆盖率 > 80%

### 性能指标
- 首次加载时间 < 3秒
- 编辑操作响应时间 < 500ms
- 内存使用 < 200MB

### 用户体验
- 界面响应式设计
- 操作流程直观
- 错误提示友好

---

**文档版本**: v1.0  
**创建日期**: 2025-01-22  
**预计完成**: 2025-05-22  
**负责人**: 开发团队


高级图书组件已注册 AdvancedBookComponents.ts:25:13
简化组件管理器初始化完成 SimpleComponentManager.ts:24:13
[EDITOR] can't access property "handlers", this.toolbarConfig is undefined TypeError: can't access property "handlers", this.toolbarConfig is undefined
    getDefaultModules RichTextEditor.ts:90
    RichTextEditor RichTextEditor.ts:35
    initializeFeatures BookEditor.ts:303
    safeExecute ErrorHandler.ts:307
    initializeFeatures BookEditor.ts:298
    BookEditor BookEditor.ts:63
    <anonymous> main.ts:6
ErrorHandler.ts:107:17
图书编辑器已启动 main.ts:111:9
💡 提示: 按 Ctrl+Shift+D 打开开发者工具 main.ts:112:9
GrapesJS 编辑器加载完成 BookEditor.ts:148:15


修复以上的错误，再开发下面的功能：
下一步计划：


    完善富文本功能: 表格编辑、图片上传、样式主题

    模板系统开发: 页面模板、章节模板设计器

    导出功能: PDF、EPUB、Word 多格式导出

    性能优化: 大文档编辑性能优化


第三阶段进展顺利，章节管理和富文本编辑功能大大提升了图书编辑的专业性和效率！

​